class CarrierNetworkBuilderLanesController < ApplicationController

  before_action :require_login
  before_action :set_carrier_network_builder
  before_action :set_lane, only: %i(show edit update destroy)

  def index
    @lanes = @carrier_network_builder.lanes
    render layout: 'core'
  end

  def show
  end

  def new
    @lane = @carrier_network_builder.lanes.build
  end

  def edit
  end

  def create
    @lane = @carrier_network_builder.lanes.build(lane_params)

    if @lane.save
      respond_to do |format|
        format.html do
          redirect_to carrier_network_builder_lanes_path(@carrier_network_builder),
                      notice: 'Lane was successfully created.'
        end
        format.turbo_stream
      end
    else
      respond_to do |format|
        format.html { render :new, status: :unprocessable_entity }
        format.turbo_stream { render :new, status: :unprocessable_entity }
      end
    end
  end

  def update
    if @lane.update(lane_params)
      respond_to do |format|
        format.html do
          redirect_to carrier_network_builder_lanes_path(@carrier_network_builder),
                      notice: 'Lane was successfully updated.'
        end
        format.turbo_stream
      end
    else
      respond_to do |format|
        format.html { render :edit, status: :unprocessable_entity }
        format.turbo_stream { render :edit, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @lane.destroy!
    respond_to do |format|
      format.html do
        redirect_to carrier_network_builder_lanes_path(@carrier_network_builder),
                    notice: 'Lane was successfully deleted.'
      end
      format.turbo_stream
    end
  end

  private

  def set_carrier_network_builder
    @carrier_network_builder = CarrierNetworkBuilder.where(user: current_user).find(params[:carrier_network_builder_id])
  end

  def set_lane
    @lane = @carrier_network_builder.lanes.find(params[:id])
  end

  def lane_params
    params.expect(
      carrier_network_builder_lane: [:origin_type, :origin_id, :destination_type, :destination_id,
                                     :volume, :frequency, :notes, { filters: {} }]
    ).except(:origin_type, :destination_type) # Remove type params since columns don't exist
  end
end
