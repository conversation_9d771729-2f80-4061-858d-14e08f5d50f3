# == Schema Information
#
# Table name: carrier_network_builders
#
#  id         :bigint           not null, primary key
#  name       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  user_id    :bigint
#
# Indexes
#
#  index_carrier_network_builders_on_user_id  (user_id)
#
class CarrierNetworkBuilder < ApplicationRecord
  belongs_to :user, optional: true
  has_many :lanes, class_name: 'CarrierNetworkBuilderLane', dependent: :destroy
end
