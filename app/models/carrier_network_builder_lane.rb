# == Schema Information
#
# Table name: carrier_network_builder_lanes
#
#  id                         :bigint           not null, primary key
#  destination_type           :string           default("city"), not null
#  filters                    :jsonb
#  frequency                  :string
#  notes                      :text
#  origin_type                :string           default("city"), not null
#  volume                     :string
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  carrier_network_builder_id :bigint           not null
#  destination_id             :string
#  origin_id                  :string
#
# Indexes
#
#  idx_on_carrier_network_builder_id_4173102239  (carrier_network_builder_id)
#
# Foreign Keys
#
#  fk_rails_60ac8dd0e2  (carrier_network_builder_id => carrier_network_builders.id) ON DELETE => cascade
#
class CarrierNetworkBuilderLane < ApplicationRecord
  belongs_to :carrier_network_builder

  enum :origin_type, %w(city region state).index_with(&:itself), scopes: false, instance_methods: false
  enum :destination_type, %w(city region state).index_with(&:itself), scopes: false, instance_methods: false

  has_many :entities, class_name: 'CarrierNetworkBuilderLaneEntity', dependent: :destroy

  after_commit on: %i(create update) do
    CarrierNetworkBuilderLanes::RefreshEntitiesJob.perform_async(id)
  end

  def origin
    return Nullable.object(:city) if origin_id.blank?
    model_lookup.fetch(origin_type).find(origin_id)
  end

  def destination
    return Nullable.object(:city) if destination_id.blank?
    model_lookup.fetch(destination_type).find(destination_id)
  end

  def filters_form
    Forms::LaneSearch.new(filters)
  end

  private

  def model_lookup
    { 'city' => City, 'region' => Geo::Region, 'state' => Geo::State }
  end
end
