module CarrierNetworkBuilderLanes
  class CreateFromChatbotResponse
    include Callable

    attr_reader :carrier_network_builder, :response_data, :elasticsearch_query

    def initialize(carrier_network_builder:, chatbot_response:)
      @carrier_network_builder = carrier_network_builder
      @response_data = chatbot_response[:data]
      @elasticsearch_query = chatbot_response[:elasticsearch_query]
    end

    def call
      lane_params = build_lane_params
      carrier_network_builder.lanes.create!(lane_params)
    end

    private

    def build_lane_params
      {
        # Store the hierarchical location IDs directly - no need to determine types
        origin_id: extract_origin_id,
        destination_id: extract_destination_id,
        # Optional: store explicit types if we want to override inference
        # origin_type: nil, # Let the model infer from ID
        # destination_type: nil, # Let the model infer from ID
        volume: response_data[:volume],
        frequency: response_data[:frequency],
        filters: extract_filters
      }
    end

    def extract_origin_id
      # The chatbot response contains the hierarchical location ID directly
      response_data[:origin_location_id]
    end

    def extract_destination_id
      # The chatbot response contains the hierarchical location ID directly
      response_data[:destination_location_id]
    end

    def extract_filters
      # Extract the Elasticsearch filters and convert them to the format expected by Forms::LaneSearch
      filters = elasticsearch_query[:filters] || {}
      
      # Convert array IDs back to the format expected by the form
      converted_filters = {}
      
      # Map Elasticsearch filter keys to form field names
      filter_mappings = {
        'truck_type_ids' => 'truck_type',
        'shipment_type_ids' => 'shipment_type', 
        'specialized_service_ids' => 'specialized_service',
        'freight_ids' => 'freight'
      }
      
      filter_mappings.each do |es_key, form_key|
        if filters[es_key].present?
          converted_filters[form_key] = filters[es_key]
        end
      end
      
      # Include other filters as-is
      other_filters = filters.except(*filter_mappings.keys)
      converted_filters.merge!(other_filters)
      
      converted_filters
    end
  end
end
