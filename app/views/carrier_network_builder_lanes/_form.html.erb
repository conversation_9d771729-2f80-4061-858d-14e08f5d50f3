<% selectize_options = {
  closeAfterSelect: true, selectOnTab: true, hidePlaceholder: true, plugins: ['remove_button']
}
%>

<%= form_with model: lane,
              url:,
              builder: ApplicationFormBuilder,
              data: { controller: 'forms--validation forms--fields' },
              class: 'space-y-6' do |form| %>
  <% if lane.errors.any? %>
    <div class="rounded-md bg-red-50 p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <%= svg_tag 'exclamation-circle-solid', class: 'h-5 w-5 text-red-400' %>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            <%= pluralize(lane.errors.count, "error") %> prohibited this lane from being saved:
          </h3>
          <div class="mt-2 text-sm text-red-700">
            <ul class="list-disc pl-5 space-y-1">
              <% lane.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
    Lane Details
  </h3>

  <!-- Origin Section -->
  <div class="space-y-4">
    <fieldset class="space-y-1.5">
      <legend class="text-black font-semibold text-sm">Where is the origin?</legend>
      <ul class="flex flex-wrap items-center gap-2">
        <% CarrierNetworkBuilderLane.origin_types.each_key do |type| %>
          <li class="flex items-center">
            <%= form.mobile_radio_button_with_label :origin_type, type, required: true,
                                                    aria: { controls: form.field_id(:origin_type, type, :fieldset) },
                                                    data: { action: 'input->forms--fields#enable' } do %>
              <%= type.titleize %>
            <% end %>
          </li>
        <% end %>
      </ul>
    </fieldset>

    <fieldset <%= lane.origin_type == 'city' ? nil : 'disabled' %> class="disabled:hidden"
              id="<%= form.field_id(:origin_type, :city, :fieldset) %>">
      <div class="error-container">
        <% origin_city_options = [] %>

        <% if lane.origin_type == 'city' && lane.origin_id.present? %>
          <% origin_city = lane.origin %>
          <% origin_city_options = [[origin_city.label, origin_city.id]] %>
        <% end %>

        <%= form.select :origin_id, origin_city_options, {},
                        required: true, class: 'rounded-sm', placeholder: 'City, State',
                        data: { controller: 'city-select',
                                city_select_options_value: selectize_options.except(:plugins).to_json } %>
      </div>
    </fieldset>

    <fieldset <%= lane.origin_type == 'state' ? nil : 'disabled' %> class="disabled:hidden"
              id="<%= form.field_id(:origin_type, :state, :fieldset) %>">
      <div class="error-container">
        <% options = Geo::Country.all.flat_map { |country| country.states.each.map { |s| [s.name, s.id] } } %>
        <%= form.select :origin_id, options_for_select(options, lane.origin_type == 'state' ? lane.origin_id : nil),
                        { prompt: 'Select a state' },
                        { required: true, class: 'rounded-sm',
                          data: { controller: 'selectize', selectize_options_value: selectize_options.to_json } } %>
      </div>
    </fieldset>

    <fieldset <%= lane.origin_type == 'region' ? nil : 'disabled' %> class="disabled:hidden"
              id="<%= form.field_id(:origin_type, :region, :fieldset) %>">
      <div class="error-container">
        <%= form.select :origin_id, options_for_select(Geo::Country.find(:us).regions.each.map { |r| [r.name, r.id] }, lane.origin_type == 'region' ? lane.origin_id : nil),
                        { prompt: 'Select a region' },
                        { required: true, class: 'rounded-sm',
                          data: { controller: 'selectize', selectize_options_value: selectize_options.to_json } } %>
      </div>
    </fieldset>
  </div>

  <%= render HorizontalRuleComponent.new %>

  <!-- Destination Section -->
  <div class="space-y-4">
    <fieldset class="space-y-1.5">
      <legend class="text-black font-semibold text-sm">Where is the destination?</legend>
      <ul class="flex flex-wrap items-center gap-2">
        <% CarrierNetworkBuilderLane.destination_types.each_key do |type| %>
          <li class="flex items-center">
            <%= form.mobile_radio_button_with_label :destination_type, type, required: true,
                                                    aria: { controls: form.field_id(:destination_type, type, :fieldset) },
                                                    data: { action: 'input->forms--fields#enable' } do %>
              <%= type.titleize %>
            <% end %>
          </li>
        <% end %>
      </ul>
    </fieldset>

    <fieldset <%= lane.destination_type == 'city' ? nil : 'disabled' %> class="disabled:hidden"
              id="<%= form.field_id(:destination_type, :city, :fieldset) %>">
      <div class="error-container">
        <% destination_city_options = [] %>
        <% if lane.destination_type == 'city' && lane.destination_id.present? %>
          <% destination_city = lane.destination %>
          <% destination_city_options = [[destination_city.label, destination_city.id]] %>
        <% end %>
        <%= form.select :destination_id, destination_city_options, {},
                        required: true, class: 'rounded-sm', placeholder: 'City, State',
                        data: { controller: 'city-select',
                                city_select_options_value: selectize_options.except(:plugins).to_json } %>
      </div>
    </fieldset>

    <fieldset <%= lane.destination_type == 'state' ? nil : 'disabled' %> class="disabled:hidden"
              id="<%= form.field_id(:destination_type, :state, :fieldset) %>">
      <div class="error-container">
        <% options = Geo::Country.all.flat_map { |country| country.states.each.map { |s| [s.name, s.id] } } %>
        <%= form.select :destination_id, options_for_select(options, lane.destination_type == 'state' ? lane.destination_id : nil),
                        { prompt: 'Select a state' },
                        { required: true, class: 'rounded-sm',
                          data: { controller: 'selectize', selectize_options_value: selectize_options.to_json } } %>
      </div>
    </fieldset>

    <fieldset <%= lane.destination_type == 'region' ? nil : 'disabled' %> class="disabled:hidden"
              id="<%= form.field_id(:destination_type, :region, :fieldset) %>">
      <div class="error-container">
        <% options = Geo::Country.find(:us).regions.each.map { |r| [r.name, r.id] } %>
        <%= form.select :destination_id, options_for_select(options, lane.destination_type == 'region' ? lane.destination_id : nil),
                        { prompt: 'Select a region' },
                        { required: true, class: 'rounded-sm',
                          data: { controller: 'selectize', selectize_options_value: selectize_options.to_json } } %>
      </div>
    </fieldset>
  </div>

  <%= render HorizontalRuleComponent.new %>

  <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
    <div class="flex flex-col gap-1">
      <%= form.label :volume, class: 'text-sm text-black font-semibold' %>
      <%= form.text_field :volume,
                          placeholder: 'e.g., 10-20 loads/week',
                          class: 'input w-full' %>
    </div>

    <div class="flex flex-col gap-1">
      <%= form.label :frequency, class: 'text-sm text-black font-semibold' %>
      <%= form.text_field :frequency,
                          placeholder: 'e.g., Daily, Weekly, Monthly',
                          class: 'input w-full' %>
    </div>
  </div>

  <%= render HorizontalRuleComponent.new %>

  <%= form.fields_for :filters, lane.filters_form do |filters| %>
    <% selectize_options = {
      selectOnTab: true, hidePlaceholder: true, plugins: %w(remove_button clear_button)
    } %>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
      <div class="flex flex-col gap-1">
        <%= filters.label :freight, 'Freight', class: 'text-sm text-black font-semibold' %>
        <%= filters.select :freight, Records[:freights].all.pluck(:name, :id), {},
                           multiple: true, class: 'rounded-sm', placeholder: 'Select the freight(s)',
                           data: { controller: 'selectize', selectize_options_value: selectize_options.to_json } %>
      </div>

      <div class="flex flex-col gap-1">
        <%= filters.label :truck_type, 'Truck Type', class: 'text-sm text-black font-semibold' %>
        <%= filters.select :truck_type, Records[:truck_types].all.pluck(:name, :id), {},
                           multiple: true, class: 'rounded-sm', placeholder: 'Select the truck type(s)',
                           data: { controller: 'selectize', selectize_options_value: selectize_options.to_json } %>
      </div>

      <div class="flex flex-col gap-1">
        <%= filters.label :shipment_type, 'Shipment Type', class: 'text-sm text-black font-semibold' %>
        <%= filters.select :shipment_type, Records[:shipment_types].all.pluck(:name, :id), {},
                           multiple: true, class: 'rounded-sm', placeholder: 'Select the shipment type(s)',
                           data: { controller: 'selectize', selectize_options_value: selectize_options.to_json } %>
      </div>

      <div class="flex flex-col gap-1">
        <%= filters.label :specialized_service, 'Specialized Service', class: 'text-sm text-black font-semibold' %>
        <%= filters.select :specialized_service, Records[:specialized_services].all.pluck(:name, :id), {},
                           multiple: true, class: 'rounded-sm', placeholder: 'Select the specialized service(s)',
                           data: { controller: 'selectize', selectize_options_value: selectize_options.to_json } %>
      </div>
    </div>
  <% end %>

  <div>
    <div class="error-container">
      <div class="flex flex-col gap-1">
        <%= form.label :notes, class: 'text-sm text-black font-semibold' %>
        <%= form.text_area :notes,
                           rows: 3,
                           placeholder: 'Additional notes about this lane...',
                           class: 'input w-full' %>
      </div>
    </div>
  </div>

  <div class="flex justify-end space-x-3">
    <button type=submit class="btn default primary"><%= lane.persisted? ? 'Update Lane' : 'Create Lane' %></button>
  </div>
<% end %>
