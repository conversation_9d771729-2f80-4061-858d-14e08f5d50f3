<% content_for :title, @carrier_network_builder.name %>

<div class="flex justify-between items-center mb-6">
  <div>
    <h1 class="text-2xl font-bold text-gray-900"><%= @carrier_network_builder.name %></h1>
    <p class="text-sm text-gray-500 mt-1">
      Created <%= time_ago_in_words(@carrier_network_builder.created_at) %> ago
    </p>
  </div>
  <div class="flex items-center space-x-3">
    <%= link_to 'Edit', edit_carrier_network_builder_path(@carrier_network_builder),
                class: 'btn secondary' %>
    <%= link_to 'Add Lane', new_carrier_network_builder_lane_path(@carrier_network_builder),
                class: 'btn primary' %>
  </div>
</div>

<div class="bg-white shadow sm:rounded-lg">
  <div class="px-4 py-5 sm:p-6">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg leading-6 font-medium text-gray-900">
        Lanes (<%= @lanes.count %>)
      </h3>
      <%= link_to 'Manage Lanes', carrier_network_builder_lanes_path(@carrier_network_builder),
                  class: 'text-primary hover:text-primary-dark text-sm font-medium' %>
    </div>

    <% if @lanes.any? %>
      <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table class="min-w-full divide-y divide-gray-300">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Origin
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Destination
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Volume
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Frequency
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% @lanes.each do |lane| %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div class="flex items-center">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                      <%= lane.origin_type.capitalize %>
                    </span>
                    <%= lane.origin.label %>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div class="flex items-center">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                      <%= lane.destination_type.capitalize %>
                    </span>
                    <%= lane.destination.label %>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= lane.volume.presence || '-' %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= lane.frequency.presence || '-' %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <%= link_to 'View', carrier_network_builder_lane_path(@carrier_network_builder, lane),
                              class: 'text-primary hover:text-primary-dark mr-3' %>
                  <%= link_to 'Edit', edit_carrier_network_builder_lane_path(@carrier_network_builder, lane),
                              class: 'text-gray-600 hover:text-gray-900 mr-3' %>
                  <%= link_to 'Delete', carrier_network_builder_lane_path(@carrier_network_builder, lane),
                              method: :delete,
                              data: { confirm: 'Are you sure?' },
                              class: 'text-red-600 hover:text-red-900' %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    <% else %>
      <div class="text-center py-8">
        <div class="mx-auto h-12 w-12 text-gray-400">
          <%= svg_tag 'road-regular', class: 'h-12 w-12' %>
        </div>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No lanes</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by adding a lane to this network builder.</p>
        <div class="mt-6">
          <%= link_to 'Add Lane', new_carrier_network_builder_lane_path(@carrier_network_builder),
                      class: 'btn primary' %>
        </div>
      </div>
    <% end %>
  </div>
</div>

<div class="mt-6">
  <%= link_to 'Back to Network Builders', carrier_network_builders_path,
              class: 'text-gray-600 hover:text-gray-900 text-sm font-medium' %>
</div>
