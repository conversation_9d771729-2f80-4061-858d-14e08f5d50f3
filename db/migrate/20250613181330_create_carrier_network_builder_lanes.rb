class CreateCarrierNetworkBuilderLanes < ActiveRecord::Migration[8.0]
  def change
    create_table :carrier_network_builder_lanes do |t|
      t.references :carrier_network_builder, null: false, foreign_key: { on_delete: :cascade }
      t.string :origin_type, null: false, default: 'city'
      t.string :origin_id
      t.string :destination_type, null: false, default: 'city'
      t.string :destination_id
      t.string :volume
      t.string :frequency
      t.jsonb :filters, default: {}
      t.text :notes

      t.timestamps
    end
  end
end
