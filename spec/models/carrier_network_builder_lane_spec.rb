require 'rails_helper'

RSpec.describe CarrierNetworkBuilderLane, type: :model do
  describe 'associations' do
    it { should belong_to(:carrier_network_builder) }
  end



  describe 'factory' do
    it 'has a valid factory' do
      expect(build(:carrier_network_builder_lane)).to be_valid
    end
  end

  describe '#origin' do
    context 'when origin_id is blank' do
      let(:lane) { build(:carrier_network_builder_lane, origin_id: nil) }

      it 'returns a nullable city object' do
        expect(lane.origin).to be_a(Nullable::City)
      end
    end

    context 'when origin_id is present' do
      let(:lane) { build(:carrier_network_builder_lane, origin_id: 'united-states:illinois:chicago') }
      let(:location) { instance_double(LaneSearches::Location) }
      let(:city) { create(:city) }

      before do
        allow(LaneSearches::Location).to receive(:new).with('united-states:illinois:chicago').and_return(location)
        allow(location).to receive(:record).and_return(city)
      end

      it 'uses LaneSearches::Location to find the origin' do
        expect(lane.origin).to eq(city)
      end
    end
  end

  describe '#destination' do
    context 'when destination_id is blank' do
      let(:lane) { build(:carrier_network_builder_lane, destination_id: nil) }

      it 'returns a nullable city object' do
        expect(lane.destination).to be_a(Nullable::City)
      end
    end

    context 'when destination_id is present' do
      let(:lane) { build(:carrier_network_builder_lane, destination_id: 'united-states:california:los-angeles') }
      let(:location) { instance_double(LaneSearches::Location) }
      let(:city) { create(:city) }

      before do
        allow(LaneSearches::Location).to receive(:new).with('united-states:california:los-angeles').and_return(location)
        allow(location).to receive(:record).and_return(city)
      end

      it 'uses LaneSearches::Location to find the destination' do
        expect(lane.destination).to eq(city)
      end
    end
  end

  describe '#origin_type' do
    context 'when origin_id is blank' do
      let(:lane) { build(:carrier_network_builder_lane, origin_id: nil) }

      it 'returns nil' do
        expect(lane.origin_type).to be_nil
      end
    end

    context 'when origin_id is present' do
      let(:lane) { build(:carrier_network_builder_lane, origin_id: 'united-states:illinois:chicago') }
      let(:location) { instance_double(LaneSearches::Location, type: :city) }

      before do
        allow(LaneSearches::Location).to receive(:new).with('united-states:illinois:chicago').and_return(location)
      end

      it 'infers the type from the ID' do
        expect(lane.origin_type).to eq('city')
      end
    end

    context 'when ID format is invalid' do
      let(:lane) { build(:carrier_network_builder_lane, origin_id: 'invalid-format') }

      before do
        allow(LaneSearches::Location).to receive(:new).with('invalid-format').and_raise(ArgumentError)
      end

      it 'returns nil' do
        expect(lane.origin_type).to be_nil
      end
    end
  end

  describe '#destination_type' do
    context 'when destination_id is blank' do
      let(:lane) { build(:carrier_network_builder_lane, destination_id: nil) }

      it 'returns nil' do
        expect(lane.destination_type).to be_nil
      end
    end

    context 'when destination_id is present' do
      let(:lane) { build(:carrier_network_builder_lane, destination_id: 'united-states:texas') }
      let(:location) { instance_double(LaneSearches::Location, type: :state) }

      before do
        allow(LaneSearches::Location).to receive(:new).with('united-states:texas').and_return(location)
      end

      it 'infers the type from the ID' do
        expect(lane.destination_type).to eq('state')
      end
    end

    context 'when ID format is invalid' do
      let(:lane) { build(:carrier_network_builder_lane, destination_id: 'invalid-format') }

      before do
        allow(LaneSearches::Location).to receive(:new).with('invalid-format').and_raise(ArgumentError)
      end

      it 'returns nil' do
        expect(lane.destination_type).to be_nil
      end
    end
  end
end
