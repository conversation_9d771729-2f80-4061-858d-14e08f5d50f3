require 'rails_helper'

RSpec.describe CarrierNetworkBuilderLane, type: :model do
  describe 'associations' do
    it { should belong_to(:carrier_network_builder) }
  end

  describe 'enums' do
    it { should define_enum_for(:origin_type).with_values(%w[city region state].index_with(&:itself)) }
    it { should define_enum_for(:destination_type).with_values(%w[city region state].index_with(&:itself)) }
  end

  describe 'factory' do
    it 'has a valid factory' do
      expect(build(:carrier_network_builder_lane)).to be_valid
    end
  end

  describe '#origin' do
    context 'when origin_id is blank' do
      let(:lane) { build(:carrier_network_builder_lane, origin_id: nil) }

      it 'returns a nullable city object' do
        expect(lane.origin).to be_a(Nullable::City)
      end
    end

    context 'when origin_type is explicitly set' do
      let(:lane) { build(:carrier_network_builder_lane, origin_type: 'city', origin_id: '123') }

      it 'uses the explicit type to find the origin' do
        expect(City).to receive(:find).with('123')
        lane.origin
      end
    end

    context 'when origin_type is nil (inferred from ID)' do
      let(:lane) { build(:carrier_network_builder_lane, origin_type: nil, origin_id: 'united-states:illinois:chicago') }
      let(:location) { instance_double(LaneSearches::Location) }
      let(:city) { create(:city) }

      before do
        allow(LaneSearches::Location).to receive(:new).with('united-states:illinois:chicago').and_return(location)
        allow(location).to receive(:record).and_return(city)
      end

      it 'uses LaneSearches::Location to infer type and find the origin' do
        expect(lane.origin).to eq(city)
      end
    end
  end

  describe '#destination' do
    context 'when destination_id is blank' do
      let(:lane) { build(:carrier_network_builder_lane, destination_id: nil) }

      it 'returns a nullable city object' do
        expect(lane.destination).to be_a(Nullable::City)
      end
    end

    context 'when destination_type is explicitly set' do
      let(:lane) { build(:carrier_network_builder_lane, destination_type: 'city', destination_id: '456') }

      it 'uses the explicit type to find the destination' do
        expect(City).to receive(:find).with('456')
        lane.destination
      end
    end

    context 'when destination_type is nil (inferred from ID)' do
      let(:lane) { build(:carrier_network_builder_lane, destination_type: nil, destination_id: 'united-states:california:los-angeles') }
      let(:location) { instance_double(LaneSearches::Location) }
      let(:city) { create(:city) }

      before do
        allow(LaneSearches::Location).to receive(:new).with('united-states:california:los-angeles').and_return(location)
        allow(location).to receive(:record).and_return(city)
      end

      it 'uses LaneSearches::Location to infer type and find the destination' do
        expect(lane.destination).to eq(city)
      end
    end
  end

  describe '#inferred_origin_type' do
    context 'when origin_type is set' do
      let(:lane) { build(:carrier_network_builder_lane, origin_type: 'city') }

      it 'returns the explicit type' do
        expect(lane.inferred_origin_type).to eq('city')
      end
    end

    context 'when origin_type is nil' do
      let(:lane) { build(:carrier_network_builder_lane, origin_type: nil, origin_id: 'united-states:illinois:chicago') }
      let(:location) { instance_double(LaneSearches::Location, type: :city) }

      before do
        allow(LaneSearches::Location).to receive(:new).with('united-states:illinois:chicago').and_return(location)
      end

      it 'infers the type from the ID' do
        expect(lane.inferred_origin_type).to eq('city')
      end
    end

    context 'when origin_id is blank' do
      let(:lane) { build(:carrier_network_builder_lane, origin_type: nil, origin_id: nil) }

      it 'returns nil' do
        expect(lane.inferred_origin_type).to be_nil
      end
    end
  end

  describe '#inferred_destination_type' do
    context 'when destination_type is set' do
      let(:lane) { build(:carrier_network_builder_lane, destination_type: 'state') }

      it 'returns the explicit type' do
        expect(lane.inferred_destination_type).to eq('state')
      end
    end

    context 'when destination_type is nil' do
      let(:lane) { build(:carrier_network_builder_lane, destination_type: nil, destination_id: 'united-states:texas') }
      let(:location) { instance_double(LaneSearches::Location, type: :state) }

      before do
        allow(LaneSearches::Location).to receive(:new).with('united-states:texas').and_return(location)
      end

      it 'infers the type from the ID' do
        expect(lane.inferred_destination_type).to eq('state')
      end
    end

    context 'when destination_id is blank' do
      let(:lane) { build(:carrier_network_builder_lane, destination_type: nil, destination_id: nil) }

      it 'returns nil' do
        expect(lane.inferred_destination_type).to be_nil
      end
    end
  end
end
